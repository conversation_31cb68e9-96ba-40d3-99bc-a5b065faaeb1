import mongoose, { Document, Schema, Model, Types } from "mongoose";
import * as bcrypt from "bcrypt";

export interface IUser extends Document {
    organizationId: Types.ObjectId,
    id?: Number;
    name?: string;
    firstName?: string;
    lastName?: string;
    countryCode?: string;
    mobile?: string;
    email?: string;
    isActive: boolean;
    newUser: boolean;
    role: Types.ObjectId;
    assignedPolicies: Types.ObjectId[];
    restrictedPolicies: Types.ObjectId[];
    salt?: string;
    password?: string;
    pin?: string;
    parent?: Types.ObjectId;
    createdAt: Date;
    updatedAt: Date;
    validatePassword(password: string): Promise<boolean>;
    validatePin(pin: string): Promise<boolean>;
}

// Define the schema
const userSchema = new Schema<IUser>({
    organizationId: { type: Schema.Types.ObjectId, required: true, ref: 'User' },
    id: { type: Number, required: false },
    name: { type: String, required: false },
    firstName: { type: String, required: false },
    lastName: { type: String, required: false, default: "" },
    mobile: {
        type: String,
        required: false,
    },
    countryCode: { type: String, required: false },
    email: {
        type: String,
        required: false,
        lowercase: true,
    },
    isActive: { type: Boolean, default: true },
    newUser: { type: Boolean, default: false },
    role: {
        type: Schema.Types.ObjectId,
        ref: 'Role',
        required: true
    },
    assignedPolicies: [{
        type: Schema.Types.ObjectId,
        ref: 'Policy',
        default: []
    }],
    restrictedPolicies: [{
        type: Schema.Types.ObjectId,
        ref: 'Policy',
        default: []
    }],
    salt: { type: String, required: false },
    password: { type: String, required: false },
    pin: {
        type: String,
        required: false,
        select: false
    },
    parent: { type: Schema.Types.ObjectId, ref: 'User', required: false }
}, { timestamps: true });


// Create and export the model
export const USER_COLLECTION = 'users';
export const User = mongoose.model<IUser>(USER_COLLECTION, userSchema);
