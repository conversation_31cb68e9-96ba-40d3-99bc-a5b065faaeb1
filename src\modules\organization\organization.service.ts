
import { getCollection } from "../../common/database/db.module";
import { ENUM_ROLE_TYPE } from "../role/role.enum";
import { User } from "../user/user.model";

export const getOrganization = async (organizationId: string) => {
  const user = await User.find({ _id: organizationId, role: global.roleMap.get(ENUM_ROLE_TYPE.ORGANIZATION) });
  return user
}

export const validateOrganization = async () => {
  try {
    if (!global.config.organizationId) {
      throw new Error('Organization ID is required');
    }
    const organization = await getOrganization(global.config.organizationId)
    if (!organization || organization.length === 0) {
      throw new Error('Organization not found');
    }
    return organization[0];
  } catch (error) {
    console.error('Error validating organization:', error);
    process.exit(10)
  }
}