import { LoggerConfig } from '../../common/logger/log.module';
import {
  IMigrationStats,
  IBatchResult,
  MigrationPhase,
  IMigrationStatus,
  IMigrationProgress
} from './user.migration.types';

const logger = LoggerConfig('user.migration.logger');

/**
 * Migration logger utility for comprehensive logging and status tracking
 */
export class MigrationLogger {
  private startTime: Date;
  private currentPhase: MigrationPhase;
  private phaseStartTime: Date;

  constructor() {
    this.startTime = new Date();
    this.currentPhase = MigrationPhase.INITIALIZATION;
    this.phaseStartTime = new Date();
  }

  /**
   * Log migration start
   */
  logMigrationStart(totalRecords: number, batchSize: number): void {
    this.startTime = new Date();
    this.setPhase(MigrationPhase.INITIALIZATION);
    
    logger.info('='.repeat(80));
    logger.info('🚀 STARTING USER MIGRATION');
    logger.info('='.repeat(80));
    logger.info(`📊 Migration Configuration:`);
    logger.info(`   • Total Records: ${totalRecords.toLocaleString()}`);
    logger.info(`   • Batch Size: ${batchSize.toLocaleString()}`);
    logger.info(`   • Total Batches: ${Math.ceil(totalRecords / batchSize).toLocaleString()}`);
    logger.info(`   • Started At: ${this.startTime.toISOString()}`);
    logger.info('='.repeat(80));
  }

  /**
   * Set current migration phase
   */
  setPhase(phase: MigrationPhase, message?: string): void {
    const previousPhase = this.currentPhase;
    const phaseEndTime = new Date();
    const phaseDuration = phaseEndTime.getTime() - this.phaseStartTime.getTime();

    if (previousPhase !== MigrationPhase.INITIALIZATION) {
      logger.info(`✅ Completed phase: ${previousPhase} (${this.formatDuration(phaseDuration)})`);
    }

    this.currentPhase = phase;
    this.phaseStartTime = new Date();
    
    const phaseMessage = message || this.getPhaseMessage(phase);
    logger.info(`🔄 Starting phase: ${phase} - ${phaseMessage}`);
  }

  /**
   * Log batch start
   */
  logBatchStart(batchNumber: number, totalBatches: number, batchSize: number): void {
    logger.info(`\n📦 Processing Batch ${batchNumber}/${totalBatches}`);
    logger.info(`   • Batch Size: ${batchSize} records`);
    logger.info(`   • Progress: ${((batchNumber - 1) / totalBatches * 100).toFixed(1)}%`);
  }

  /**
   * Log batch completion
   */
  logBatchComplete(batchResult: IBatchResult, totalBatches: number): void {
    const successRate = (batchResult.successCount / batchResult.batchSize * 100).toFixed(1);
    const recordsPerSecond = (batchResult.batchSize / (batchResult.processingTime / 1000)).toFixed(1);
    
    logger.info(`✅ Batch ${batchResult.batchNumber}/${totalBatches} completed:`);
    logger.info(`   • Processing Time: ${this.formatDuration(batchResult.processingTime)}`);
    logger.info(`   • Success: ${batchResult.successCount}/${batchResult.batchSize} (${successRate}%)`);
    logger.info(`   • Failed: ${batchResult.errorCount}`);
    logger.info(`   • Speed: ${recordsPerSecond} records/second`);
    
    if (batchResult.errorCount > 0) {
      logger.warn(`⚠️  Batch ${batchResult.batchNumber} had ${batchResult.errorCount} errors`);
    }
  }

  /**
   * Log overall progress
   */
  logProgress(stats: IMigrationStats, currentBatch: number): void {
    const progress = this.calculateProgress(stats, currentBatch);
    const elapsed = Date.now() - this.startTime.getTime();
    const estimatedTotal = stats.totalBatches > 0 ? (elapsed / currentBatch) * stats.totalBatches : 0;
    const remaining = estimatedTotal - elapsed;

    logger.info(`\n📈 MIGRATION PROGRESS UPDATE`);
    logger.info(`   • Overall Progress: ${progress.percentComplete.toFixed(1)}%`);
    logger.info(`   • Batches: ${currentBatch}/${stats.totalBatches}`);
    logger.info(`   • Records: ${stats.totalProcessed.toLocaleString()}/${stats.totalRecords.toLocaleString()}`);
    logger.info(`   • Success Rate: ${((stats.totalSuccessful / Math.max(stats.totalProcessed, 1)) * 100).toFixed(1)}%`);
    logger.info(`   • Elapsed Time: ${this.formatDuration(elapsed)}`);
    logger.info(`   • Estimated Remaining: ${this.formatDuration(remaining)}`);
    logger.info(`   • Average Speed: ${progress.averageRecordsPerSecond?.toFixed(1) || 0} records/second`);
  }

  /**
   * Log detailed statistics
   */
  logDetailedStats(stats: IMigrationStats): void {
    logger.info(`\n📊 DETAILED STATISTICS:`);
    logger.info(`   📈 Processing Summary:`);
    logger.info(`      • Total Records: ${stats.totalRecords.toLocaleString()}`);
    logger.info(`      • Total Processed: ${stats.totalProcessed.toLocaleString()}`);
    logger.info(`      • Total Successful: ${stats.totalSuccessful.toLocaleString()}`);
    logger.info(`      • Total Failed: ${stats.totalFailed.toLocaleString()}`);
    
    logger.info(`   👥 Entity Counts:`);
    logger.info(`      • Users Created: ${stats.totalUsers.toLocaleString()}`);
    logger.info(`      • Staff Created: ${stats.totalStaff.toLocaleString()}`);
    logger.info(`      • Clients Created: ${stats.totalClients.toLocaleString()}`);
    
    logger.info(`   ❌ Error Breakdown:`);
    logger.info(`      • Validation Errors: ${stats.validationErrors.toLocaleString()}`);
    logger.info(`      • Duplicate Errors: ${stats.duplicateErrors.toLocaleString()}`);
    logger.info(`      • Database Errors: ${stats.databaseErrors.toLocaleString()}`);
    logger.info(`      • Facility Errors: ${stats.facilityErrors.toLocaleString()}`);
    logger.info(`      • Role Errors: ${stats.roleErrors.toLocaleString()}`);
    logger.info(`      • Unknown Errors: ${stats.unknownErrors.toLocaleString()}`);
  }

  /**
   * Log performance metrics
   */
  logPerformanceMetrics(stats: IMigrationStats): void {
    if (!stats.endTime || !stats.totalProcessingTime) return;

    logger.info(`\n⚡ PERFORMANCE METRICS:`);
    logger.info(`   • Total Processing Time: ${this.formatDuration(stats.totalProcessingTime)}`);
    logger.info(`   • Average Batch Time: ${this.formatDuration(stats.averageBatchTime || 0)}`);
    logger.info(`   • Records Per Second: ${stats.recordsPerSecond?.toFixed(2) || 0}`);
    logger.info(`   • Success Rate: ${((stats.totalSuccessful / Math.max(stats.totalProcessed, 1)) * 100).toFixed(2)}%`);
    
    if (stats.totalBatches > 0) {
      const batchesPerMinute = (stats.totalBatches / (stats.totalProcessingTime / 60000)).toFixed(2);
      logger.info(`   • Batches Per Minute: ${batchesPerMinute}`);
    }
  }

  /**
   * Log migration completion
   */
  logMigrationComplete(stats: IMigrationStats, csvFiles: { successful: string; failed: string }): void {
    const totalTime = stats.endTime ? stats.endTime.getTime() - this.startTime.getTime() : 0;
    const successRate = ((stats.totalSuccessful / Math.max(stats.totalProcessed, 1)) * 100).toFixed(2);
    
    logger.info('\n' + '='.repeat(80));
    logger.info('🎉 MIGRATION COMPLETED SUCCESSFULLY');
    logger.info('='.repeat(80));
    
    this.logDetailedStats(stats);
    this.logPerformanceMetrics(stats);
    
    logger.info(`\n📁 OUTPUT FILES:`);
    logger.info(`   • Successful Records: ${csvFiles.successful}`);
    logger.info(`   • Failed Records: ${csvFiles.failed}`);
    
    logger.info(`\n🏁 FINAL SUMMARY:`);
    logger.info(`   • Total Time: ${this.formatDuration(totalTime)}`);
    logger.info(`   • Overall Success Rate: ${successRate}%`);
    logger.info(`   • Started: ${this.startTime.toISOString()}`);
    logger.info(`   • Completed: ${stats.endTime?.toISOString() || 'Unknown'}`);
    
    if (stats.totalFailed > 0) {
      logger.warn(`⚠️  ${stats.totalFailed} records failed migration. Check the failed records CSV for details.`);
    }
    
    logger.info('='.repeat(80));
  }

  /**
   * Log migration failure
   */
  logMigrationFailure(error: Error, stats?: IMigrationStats): void {
    const totalTime = Date.now() - this.startTime.getTime();
    
    logger.error('\n' + '='.repeat(80));
    logger.error('💥 MIGRATION FAILED');
    logger.error('='.repeat(80));
    logger.error(`❌ Error: ${error.message}`);
    logger.error(`🕐 Failed after: ${this.formatDuration(totalTime)}`);
    logger.error(`📍 Phase: ${this.currentPhase}`);
    
    if (stats) {
      logger.error(`📊 Progress at failure:`);
      logger.error(`   • Records processed: ${stats.totalProcessed}/${stats.totalRecords}`);
      logger.error(`   • Successful: ${stats.totalSuccessful}`);
      logger.error(`   • Failed: ${stats.totalFailed}`);
    }
    
    logger.error('='.repeat(80));
    logger.error('Stack trace:', error.stack);
  }

  /**
   * Calculate migration progress
   */
  private calculateProgress(stats: IMigrationStats, currentBatch: number): IMigrationProgress {
    const percentComplete = stats.totalRecords > 0 ? (stats.totalProcessed / stats.totalRecords) * 100 : 0;
    const elapsed = Date.now() - this.startTime.getTime();
    const averageRecordsPerSecond = elapsed > 0 ? (stats.totalProcessed / (elapsed / 1000)) : 0;
    const estimatedTimeRemaining = averageRecordsPerSecond > 0 ? 
      ((stats.totalRecords - stats.totalProcessed) / averageRecordsPerSecond) * 1000 : 0;

    return {
      currentBatch,
      totalBatches: stats.totalBatches,
      currentRecord: stats.totalProcessed,
      totalRecords: stats.totalRecords,
      successfulRecords: stats.totalSuccessful,
      failedRecords: stats.totalFailed,
      percentComplete,
      estimatedTimeRemaining,
      averageRecordsPerSecond
    };
  }

  /**
   * Get phase message
   */
  private getPhaseMessage(phase: MigrationPhase): string {
    const messages = {
      [MigrationPhase.INITIALIZATION]: 'Setting up migration environment',
      [MigrationPhase.DATA_LOADING]: 'Loading and parsing CSV data',
      [MigrationPhase.VALIDATION]: 'Validating data integrity',
      [MigrationPhase.BATCH_PROCESSING]: 'Processing data in batches',
      [MigrationPhase.RESULT_GENERATION]: 'Generating result CSV files',
      [MigrationPhase.CLEANUP]: 'Cleaning up resources',
      [MigrationPhase.COMPLETED]: 'Migration completed successfully',
      [MigrationPhase.FAILED]: 'Migration failed'
    };
    
    return messages[phase] || 'Unknown phase';
  }

  /**
   * Format duration in human-readable format
   */
  private formatDuration(milliseconds: number): string {
    if (milliseconds < 1000) {
      return `${milliseconds}ms`;
    }
    
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }
}
