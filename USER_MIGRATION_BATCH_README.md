# User Migration - Batch Processing System

## Overview

The enhanced user migration system now supports efficient batch processing for handling large datasets. The system loads all CSV data into memory first, then processes it in configurable batches with comprehensive logging and result tracking.

## Key Features

### 🚀 **Batch Processing**
- Configurable batch sizes (default: 100 records per batch)
- Memory-efficient processing of large datasets
- Progress tracking with time estimates

### 📊 **Comprehensive Logging**
- Real-time progress updates
- Detailed performance metrics
- Phase-based migration tracking
- Batch-level success/failure reporting

### 📁 **Result Generation**
- Separate CSV files for successful and failed records
- Detailed migration summary with statistics
- Error reports grouped by error type
- Timestamped output files

### 🔄 **Error Handling**
- Configurable retry mechanisms
- Transaction safety with MongoDB
- Graceful failure handling
- Detailed error categorization

## Configuration

### Batch Configuration Options

```typescript
interface IBatchConfig {
  batchSize: number;           // Records per batch (default: 100)
  maxRetries: number;          // Max retry attempts (default: 3)
  retryDelayMs: number;        // Delay between retries (default: 1000ms)
  logProgressInterval: number; // Log progress every N batches (default: 10)
}
```

### Default Configuration

```typescript
const DEFAULT_BATCH_CONFIG = {
  batchSize: 100,
  maxRetries: 3,
  retryDelayMs: 1000,
  logProgressInterval: 10
};
```

## Usage Examples

### Basic Usage (Default Configuration)

```typescript
import { migrateUsers } from './user.migration';

// Use default batch configuration
await migrateUsers();
```

### Custom Configuration

```typescript
import { migrateUsers } from './user.migration';

const customConfig = {
  batchSize: 50,           // Smaller batches
  maxRetries: 5,           // More retries
  retryDelayMs: 2000,      // Longer retry delay
  logProgressInterval: 5   // More frequent logging
};

await migrateUsers('hop-migration', customConfig);
```

### Large Dataset Optimization

```typescript
const largeDatasetConfig = {
  batchSize: 200,          // Larger batches for better performance
  maxRetries: 3,           // Standard retries
  retryDelayMs: 500,       // Shorter delay
  logProgressInterval: 20  // Less frequent logging
};

await migrateUsers('hop-migration', largeDatasetConfig);
```

## Output Files

After migration completion, the following files are generated in `data/migration-results/`:

### 1. Successful Records CSV
**File:** `successful-users-YYYY-MM-DD.csv`

Contains all successfully migrated users with their database IDs.

**Columns:**
- `rowNumber`: Original CSV row number
- `id`: User ID from CSV
- `firstName`, `lastName`: User names
- `email`, `mobile`: Contact information
- `role`: User role
- `facilityId`: Facility ID
- `userId`: Generated MongoDB User ID
- `staffId`: Generated Staff ID (if applicable)
- `clientId`: Generated Client ID (if applicable)
- `processingTime`: Time taken to process (ms)
- `processedAt`: Processing timestamp
- `batchNumber`: Batch number

### 2. Failed Records CSV
**File:** `failed-users-YYYY-MM-DD.csv`

Contains all failed records with detailed error information.

**Columns:**
- `rowNumber`: Original CSV row number
- `id`: User ID from CSV
- `firstName`, `lastName`: User names
- `email`, `mobile`: Contact information
- `role`: User role
- `facilityId`: Facility ID
- `errorType`: Error category (VALIDATION, DATABASE, DUPLICATE, etc.)
- `errorMessage`: Detailed error description
- `processingTime`: Time taken before failure (ms)
- `processedAt`: Processing timestamp
- `batchNumber`: Batch number

### 3. Migration Summary CSV
**File:** `migration-summary-YYYY-MM-DD.csv`

Contains comprehensive migration statistics and performance metrics.

**Includes:**
- Total records processed
- Success/failure counts and percentages
- Error breakdown by type
- Performance metrics (processing time, records per second)
- Batch statistics

### 4. Error Report CSV (if errors exist)
**File:** `error-report-YYYY-MM-DD.csv`

Contains grouped error analysis for troubleshooting.

**Includes:**
- Error types and counts
- Sample error messages
- Affected row numbers

## Migration Process Flow

### Phase 1: Initialization
- Set up migration environment
- Connect to database
- Initialize logging

### Phase 2: Data Loading
- Load entire CSV file into memory
- Parse and validate CSV structure
- Create row number mappings

### Phase 3: Validation
- Check for duplicate users in database
- Validate required fields
- Prepare for batch processing

### Phase 4: Batch Processing
- Process users in configurable batches
- Handle parent-child relationships
- Create User, Staff, and Client records
- Track success/failure for each record

### Phase 5: Result Generation
- Generate success/failure CSV files
- Create migration summary
- Generate error reports

### Phase 6: Cleanup
- Close database connections
- Finalize logging
- Clean up resources

## Performance Considerations

### Memory Usage
- **All CSV data is loaded into memory first**
- Ensure sufficient RAM for large datasets
- Monitor memory usage during processing

### Batch Size Optimization
- **Small batches (25-50)**: Better error isolation, more detailed logging
- **Medium batches (100-200)**: Balanced performance and tracking
- **Large batches (500+)**: Better performance, less granular tracking

### Database Performance
- Uses MongoDB transactions for consistency
- Bulk inserts within each batch
- Connection pooling for efficiency

## Error Types

The system categorizes errors into the following types:

- **VALIDATION**: Missing required fields, invalid data format
- **DUPLICATE**: User already exists in database
- **DATABASE**: MongoDB operation failures
- **FACILITY**: Facility not found or mapping issues
- **ROLE**: Role-related errors
- **UNKNOWN**: Unexpected errors

## Monitoring and Troubleshooting

### Log Files
Check the daily log files in the `logs/` directory:
- `YYYY-MM-DD.user.migration.application.log`
- `YYYY-MM-DD.user.migration.batch.application.log`
- `YYYY-MM-DD.user.migration.logger.application.log`

### Progress Monitoring
The system provides real-time progress updates including:
- Current batch and total batches
- Records processed and remaining
- Success/failure counts
- Estimated time remaining
- Processing speed (records per second)

### Common Issues and Solutions

1. **Memory Issues**: Reduce batch size or increase available RAM
2. **Database Timeouts**: Reduce batch size or increase timeout settings
3. **Duplicate Errors**: Clean existing data or update duplicate detection logic
4. **Facility Mapping**: Ensure facility data is properly loaded
5. **Role Issues**: Verify role mappings are correct

## Best Practices

1. **Test with Small Datasets**: Always test with a small subset first
2. **Monitor Resources**: Watch memory and database performance
3. **Backup Data**: Always backup before running migration
4. **Review Error Reports**: Analyze failed records for patterns
5. **Adjust Configuration**: Tune batch size based on performance
6. **Validate Results**: Compare input CSV with output files

## Support

For issues or questions about the migration system:
1. Check the log files for detailed error information
2. Review the generated error reports
3. Adjust batch configuration based on your dataset size
4. Contact the development team with specific error details
