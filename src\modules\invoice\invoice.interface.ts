import { ENUM_PRODUCT_ITEM_TYPE, ENUM_PAYMENT_METHOD, ENUM_PAYMENT_STATUS, ENUM_DISCOUNT_TYPE } from '../../common/enums/enums';

export interface ICsvInvoice {
    index: number;
    invoiceId: string;
    invoiceDate: Date;
    userId: string;
    billingClientId?: string;
    facilityId: string;
    amountPaid: number;
    cartDiscountType?: ENUM_DISCOUNT_TYPE;
    cartDiscountAmount?: number;
    paymentMethod: ENUM_PAYMENT_METHOD;
    paymentStatus: ENUM_PAYMENT_STATUS;
    invoiceItemId: string;
    itemId: string;
    itemType: ENUM_PRODUCT_ITEM_TYPE;
    itemQuantity: number;
    promotion?: string;
    itemDiscountType?: ENUM_DISCOUNT_TYPE;
    itemDiscountAmount?: number;
    startDate?: Date;
    isBusiness?: boolean;
    employeeId?: string;

}


export const CsvToObjectKeyMapUser: Record<keyof ICsvInvoice, string> = {
    index: 'index',
    invoiceId: 'invoice id',
    invoiceDate: 'invoice date',
    userId: 'client id',
    billingClientId: 'billing client id',
    facilityId: 'facility id',
    amountPaid: 'amount paid',
    cartDiscountType: 'cart discount type',
    cartDiscountAmount: 'cart discount amount',
    paymentMethod: 'payment method',
    paymentStatus: 'payment status',
    invoiceItemId: 'invoice item id',
    itemId: 'item id',
    itemType: 'item type',
    itemQuantity: 'item quantity',
    promotion: 'item promotion',
    itemDiscountType: 'item discount type',
    itemDiscountAmount: 'item discount amount',
    startDate: 'start date',
    isBusiness: 'is business',
    employeeId: 'employee id',
}

export interface IInvoiceItem {
    id: string;
    itemId: string;
    itemType: ENUM_PRODUCT_ITEM_TYPE;
    quantity: number;
    discount?: {
        type: ENUM_DISCOUNT_TYPE,
        value: number
    };
    promotion?: string;
    startDate?: Date;
    promotionLabel?: string,
    promotionLabelKey?: string
}

export interface IInvoiceCart {
    facilityId: string,
    userId: string,
    items: IInvoiceItem[],
    promotionCode: string,
    discount?: {
        type: ENUM_DISCOUNT_TYPE,
        value: number
    }
}

export interface IPaymentDetail {
    paymentMethod: ENUM_PAYMENT_METHOD,
    paymentMethodId: string,
    transactionId?: string,
    amount: number,
    paymentDate: Date,
    paymentStatus: ENUM_PAYMENT_STATUS,
    paymentGateway?: string,
    description?: string,
    denominations?: {
        [key: number]: number
    }
}

export interface IInvoicePurchase {
    id: string,
    cart: IInvoiceCart,
    paymentDetails?: IPaymentDetail[],
    isSplittedPayment: boolean,
    amountPaid: number,
    platform: string,
    billingAddressId: string,
    paymentBy: string,
    date: Date,
    returnItems: string[]
}