import mongoose, { Document, Schema, Types } from 'mongoose';

// Define interfaces
export interface IAppointmentType extends Document {
  id: string;
  name: string;
  durationInMinutes: number;
  onlineBookingAllowed: boolean;
  isActive: boolean;
  image?: string;
  isFeatured?: boolean;
  serviceId?: Types.ObjectId;
  organizationId: Types.ObjectId;
  createdBy: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

// Define appointment type schema
export const AppointmentTypeSchema = new Schema<IAppointmentType>({
  id: {
    type: String,
    required: false,
  },
  name: {
    type: String,
    required: true
  },
  durationInMinutes: {
    type: Number,
    required: true
  },
  onlineBookingAllowed: {
    type: Boolean,
    default: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  image: {
    type: String,
    required: false
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  organizationId: {
    type: Schema.Types.ObjectId,
    required: true,
    ref: "User"
  }
}, {
  timestamps: false
});

// Create and export the model
export const APPOINTMENT_TYPE_COLLECTION = 'attributes';
export const AppointmentType = mongoose.model<IAppointmentType>('AppointmentType', AppointmentTypeSchema, APPOINTMENT_TYPE_COLLECTION);
