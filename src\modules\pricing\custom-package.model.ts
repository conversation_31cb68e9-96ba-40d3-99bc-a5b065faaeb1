import mongoose, { Schema, model, Document } from 'mongoose';
import { ENUM_DISCOUNT_TYPE } from '../../common/enums/enums';
import { DiscountType } from '../../utils/enums/discount.enum';


export interface ICustomPackageDocument extends Document {
    organizationId: mongoose.Types.ObjectId;
    facilityId: mongoose.Types.ObjectId;
    name: string;
    unitPrice: number;
    total: number;
    quantity: number;
    tax: number;
    hsnOrSacCode?: string;
    discount?: {
        type: ENUM_DISCOUNT_TYPE;
        value: number;
    };
    isActive: boolean;
    isTaxable: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

const DiscountSchema = new Schema<ENUM_DISCOUNT_TYPE>(
    {
        type: {
            type: String,
            enum: Object.values(DiscountType),
            required: false,
        },
        value: {
            type: Number,
            required: false,
        },
    },
    { _id: false } // Prevents Mongoose from creating _id for subdocument
);

const CustomPackageSchema = new Schema<ICustomPackageDocument>(
    {
        organizationId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Organization',
            required: true,
        },
        facilityId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Facility',
            required: true,
        },
        name: {
            type: String,
            required: true,
        },
        unitPrice: {
            type: Number,
            required: true,
        },
        total: {
            type: Number,
            required: true,
        },
        quantity: {
            type: Number,
            required: true,
        },
        tax: {
            type: Number,
            required: true,
        },
        hsnOrSacCode: {
            type: String,
            required: false,
        },
        discount: {
            type: DiscountSchema,
            required: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        isTaxable: {
            type: Boolean,
            default: true,
        },
    },
    {
        timestamps: true, // adds createdAt and updatedAt
    }
);

export const CustomPackage = model<ICustomPackageDocument>('CustomPackage', CustomPackageSchema);
