import * as fs from 'fs';
import { parse } from 'csv-parse';
import logger from '../logger/log.module';

/**
 * Interface for CSV row with row number tracking
 */
export interface ICsvRowWithNumber<T> {
  data: T;
  rowNumber: number;
}

/**
 * Parse a CSV file into an array of objects with support for nested properties
 * @param filePath Path to the CSV file
 * @returns Promise with array of objects representing each row in the CSV
 */
export function parseCSV<T>(filename: string, mapping?: Record<keyof T, string>): Promise<T[]> {
  const filePath = filename.startsWith('/') ? filename : `${process.env.DATA_FOLDER}/${filename}`;
  return new Promise((resolve, reject) => {
    const results: T[] = [];

    fs.createReadStream(filePath)
      .pipe(parse({
        columns: true,
        skip_empty_lines: true
      }))
      .on('data', (data) => {
        if (mapping) {
          data = transformFromCsvToObject<T>(data, mapping);
        }
        // Process nested properties (convert dot notation to nested objects)
        const processedData = processNestedProperties(data);
        results.push(processedData as T);
      })
      .on('end', () => resolve(results))
      .on('error', (error) => {
        logger.error(`Error parsing CSV file ${filePath}:`, error);
        reject(error);
      });
  });
}

/**
 * Parse a CSV file into an array of objects with row number tracking
 * @param filePath Path to the CSV file
 * @returns Promise with array of objects with row numbers representing each row in the CSV
 */
export function parseCSVWithRowNumbers<T>(filename: string, mapping?: Record<keyof T, string>): Promise<ICsvRowWithNumber<T>[]> {
  const filePath = filename.startsWith('/') ? filename : `${process.env.DATA_FOLDER}/${filename}`;
  logger.info(`Reading CSV file from: ${filePath}`);
  return new Promise((resolve, reject) => {
    const results: ICsvRowWithNumber<T>[] = [];
    let rowNumber = 1; // Start from 1 (header is row 0, first data row is 1)

    fs.createReadStream(filePath)
      .pipe(parse({
        columns: true,
        skip_empty_lines: true
      }))
      .on('data', (data) => {
        if (mapping) {
          data = transformFromCsvToObject(data, mapping);
        }
        // Process nested properties (convert dot notation to nested objects)
        const processedData = processNestedProperties(data);
        results.push({
          data: processedData as T,
          rowNumber: rowNumber + 1 // +1 because header is row 1, first data row is row 2
        });
        rowNumber++;
      })
      .on('end', () => resolve(results))
      .on('error', (error) => {
        logger.error(`Error parsing CSV file ${filePath}:`, error);
        reject(error);
      });
  });
}

/**
 * Process flat object with dot notation into nested objects
 * @param data Flat object with dot notation keys
 * @returns Object with proper nesting
 */
function processNestedProperties(data: Record<string, any>): Record<string, any> {
  const result: Record<string, any> = {};

  for (let key in data) {
    key = key.trim()
    if (key.includes('.')) {
      const parts = key.split('.');
      let current = result;

      // Create nested objects for all parts except the last one
      for (let i = 0; i < parts.length - 1; i++) {
        const part = parts[i];
        if (!current[part]) {
          current[part] = {};
        }
        current = current[part];
      }

      // Set the value at the deepest level
      const lastPart = parts[parts.length - 1];
      current[lastPart] = data[key];
    } else {
      data[key] = data[key].trim()
      // For non-nested properties, just copy them
      if (data[key] === 'true' || data[key] === 'True' || data[key] === 'TRUE' || data[key] === 'Yes' || data[key] === 'yes' || data[key] === 'YEAS') {
        data[key] = true;
      } else if (data[key] === 'false' || data[key] === 'False' || data[key] === 'FALSE' || data[key] === 'No' || data[key] === 'no' || data[key] === 'NO') {
        data[key] = false;
      }
      result[key] = data[key];
    }
  }

  return result;
}

// function transformFromCsvToObject<T>(data: Record<string, any>, mapping: Record<keyof T, string>): T {
//   const result: any = {};

//   for (const key in mapping) {
//     // console.log(data.hasOwnProperty("id".toLocaleLowerCase()))
//     // console.log("mapping[key]", typeof (mapping[key]))
//     // console.log("data.hasOwnProperty(mapping[key].toLocaleLowerCase())", data.hasOwnProperty(mapping[key].toLocaleLowerCase().toString()))
//     // console.log("Object.keys(data)", Object.keys(data))
//     // console.log(Object.keys(data).includes(mapping[key].toLocaleLowerCase()))
//     console.log("mapping[key]", mapping[key].toString())
//     console.log("data", data)
//     console.log("data.hasOwnProperty(mapping[key])", data.hasOwnProperty(mapping[key].toString()))
//     if (data.hasOwnProperty(mapping[key])) {
//       // console.log("result", result)
//       // console.log("key", key)
//       // console.log("data[mapping[key].toLocaleLowerCase()]", data[mapping[key].toLocaleLowerCase()])
//       result[key] = data[mapping[key].toLocaleLowerCase()];
//       // console.log("afterResultmap", result)
//     }
//   }

//   return result as T;
// }

function transformFromCsvToObject<T>(data: Record<string, any>, mapping: Record<keyof T, string>): T {
  const result: any = {};
  const normalizedData = Object.fromEntries(
    Object.entries(data).map(([k, v]) => [k.trim().toLowerCase(), v])
  );

  for (const key in mapping) {
    const mapKey = mapping[key]?.trim().toLowerCase();
    if (mapKey && normalizedData.hasOwnProperty(mapKey)) {
      result[key] = normalizedData[mapKey];
    }
  }

  return result as T;
}