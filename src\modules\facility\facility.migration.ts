import { Types } from 'mongoose';
import { LoggerConfig } from '../../common/logger/log.module';
import { connectToMongo, closeConnection } from '../../common/database/db.module';
import { Facility, IFacility } from './facility.model';
import { parseCSV } from '../../common/utils/csv-parser';

interface ICsvFacility {
  id: number;
  facilityName: string;
  mobile?: string;
  email?: string;
  address: {
    state: string;
    city: string;
    addressLine1: string;
    addressLine2?: string;
    postalCode?: number;
  };
  billingDetails: {
    billingName?: string;
    addressLine1?: string;
    addressLine2?: string;
    state?: string;
    city?: string;
    postalCode?: number;
    gstNumber?: string;
  };
  // contactName?: string;
  // profilePicture?: string;
  // gallery?: string[];
  // description?: string;
  // amenities?: string[];
  isActive?: boolean;
  // isStoreActive?: boolean;
  // paymentMethods?: Array<{
  //   name: string;
  //   shortId: string;
  //   isDefault?: boolean;
  //   imageUrl?: string;
  //   isActive?: boolean;
  //   paymentMethodId: string;
  //   addedBy?: string;
  // }>;
}

const CsvToObjectKeyMapFacility: Record<keyof ICsvFacility, string> = {
  // id, facilityName, mobile, email, address.addressLine1, address.addressLine2, address.city, address.state, address.postalCode, billingDetails.billingName, billingDetails.addressLine1, billingDetails.addressLine2, billingDetails.state, billingDetails.city, billingDetails.postalCode, billingDetails.gstNumber, isActive, Monday Start Time, Monday End Time, Tuesday Start Time, Tuesday End Time, Wednesday Start Time, Wednesday End Time, Thursday Start Time, Thursday End Time, Friday Start Time, Friday End Time, Saturday Start Time, Saturday End Time, Sunday Start Time, Sunday End Time
  id: "id",
  facilityName: "facility name",
  mobile: "mobile",
  email: "email",
  address: "address",
  billingDetails: "billing details",
  isActive: "is active"
}

const logger = LoggerConfig('facility.migration');

/**
 * Validate facility data
 */
export async function validateFacility(csvFacility: ICsvFacility): Promise<string[]> {
  const errors: string[] = [];

  // Required fields validation
  if (!csvFacility.id) {
    errors.push('SQL ID is required');
  }

  if (!csvFacility.facilityName) {
    errors.push('Facility name is required');
  }

  if (!csvFacility.address || !csvFacility.address.addressLine1) {
    errors.push('Address line 1 is required');
  }

  // Email format validation
  if (csvFacility.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(csvFacility.email)) {
    errors.push('Invalid email format');
  }

  // Mobile format validation (basic)
  if (csvFacility.mobile && !/^\d{10,15}$/.test(csvFacility.mobile.replace(/[^0-9]/g, ''))) {
    errors.push('Invalid mobile number format');
  }

  // Payment methods validation
  // if (csvFacility.paymentMethods && csvFacility.paymentMethods.length > 0) {
  //   csvFacility.paymentMethods.forEach((method, index) => {
  //     if (!method.name) {
  //       errors.push(`Payment method at index ${index} is missing name`);
  //     }
  //     if (!method.shortId) {
  //       errors.push(`Payment method at index ${index} is missing shortId`);
  //     }
  //     if (!method.paymentMethodId) {
  //       errors.push(`Payment method at index ${index} is missing paymentMethodId`);
  //     }
  //   });
  // }

  return errors;
}

/**
 * Migrate facilities data from CSV to MongoDB
 */
export async function migrateFacilities(dbName: string = 'hop-migration'): Promise<void> {
  let session: any = null;

  try {
    logger.log('Starting facility migration...');

    // Connect to database
    await connectToMongo();

    // Start a session for transaction
    const mongoose = require('mongoose');
    session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Get facilities from CSV or other source
      const facilities = await getFacilities(session);

      if (!facilities || facilities.length === 0) {
        logger.log('No valid facilities to migrate');
        return;
      }

      logger.log(`Successfully migrated ${facilities.length} facilities to MongoDB`);

      // Commit the transaction
      await session.commitTransaction();

      // Set facility in global
      // global.facilities = await getFacilityMap()
      global.facilityMap = await getFacilityIdDocMap();
      logger.log('Transaction committed successfully');
    } catch (error) {
      // Abort the transaction on error
      if (session) {
        await session.abortTransaction();
      }
      logger.error('Error in migration process:', error);
      throw error;
    }
  } catch (error) {
    logger.error('Error migrating facilities:', error);
  } finally {
    // End the session
    if (session) {
      session.endSession();
    }
    // Close the connection
    await closeConnection();
  }
}

/**
 * Get and validate facilities from source
 */
async function getFacilities(session: any): Promise<IFacility[]> {
  try {
    // This is where you would read from CSV or other data source
    // For now, we'll just return an empty array
    const csvFacilities = await parseCSV<ICsvFacility>('data/facility.csv');

    // Process and validate each facility
    const facilities: IFacility[] = [];
    const errors: string[] = [];

    for (const csvFacility of csvFacilities) {
      // Validate the facility
      const validationErrors = await validateFacility(csvFacility);

      if (validationErrors.length > 0) {
        errors.push(...validationErrors);
        continue;
      }

      const stateId = global.stateMap.get(csvFacility.address.state?.toLowerCase());
      const cityId = global.cityMap.get(csvFacility.address.city?.toLowerCase());
      if (!stateId) {
        throw new Error(`State not found for "${csvFacility.address.state}"`);
      }
      if (!cityId) {
        throw new Error(`City not found for "${csvFacility.address.city}"`);
      }

      const billingStateId = global.stateMap.get(csvFacility.billingDetails?.state?.toLowerCase());
      const billingCityId = global.cityMap.get(csvFacility.billingDetails?.city?.toLowerCase());
      if (!billingStateId) {
        throw new Error(`State not found for "${csvFacility.billingDetails?.state}"`);
      }
      if (!billingCityId) {
        throw new Error(`City not found for "${csvFacility.billingDetails?.city}"`);
      }

      // Convert string IDs to ObjectIds
      const facility = {
        id: csvFacility.id,
        organizationId: new Types.ObjectId(global.config.organizationId),
        facilityName: csvFacility.facilityName,
        mobile: csvFacility.mobile,
        email: csvFacility.email,
        address: {
          state: new Types.ObjectId(stateId),
          city: new Types.ObjectId(cityId),
          addressLine1: csvFacility.address.addressLine1,
          addressLine2: csvFacility.address.addressLine2,
          postalCode: csvFacility.address.postalCode
        },
        billingDetails: {
          billingName: csvFacility.billingDetails.billingName,
          addressLine1: csvFacility.billingDetails.addressLine1,
          addressLine2: csvFacility.billingDetails.addressLine2,
          state: new Types.ObjectId(billingStateId),
          city: new Types.ObjectId(billingCityId),
          postalCode: csvFacility.billingDetails.postalCode,
          gstNumber: csvFacility.billingDetails.gstNumber
        },
        // contactName: csvFacility.contactName,
        // profilePicture: csvFacility.profilePicture,
        // gallery: csvFacility.gallery || [],
        // description: csvFacility.description,
        // amenities: csvFacility.amenities?.map(id => new Types.ObjectId(id)) || [],
        isActive: csvFacility.isActive !== undefined ? csvFacility.isActive : true,
        // isStoreActive: csvFacility.isStoreActive !== undefined ? csvFacility.isStoreActive : false,
        // paymentMethods: csvFacility.paymentMethods?.map(method => ({
        //   name: method.name,
        //   shortId: method.shortId,
        //   isDefault: method.isDefault,
        //   imageUrl: method.imageUrl,
        //   isActive: method.isActive !== undefined ? method.isActive : true,
        //   paymentMethodId: new Types.ObjectId(method.paymentMethodId),
        //   addedBy: method.addedBy ? new Types.ObjectId(method.addedBy) : undefined
        // })) || []
      };

      facilities.push(facility as IFacility);
    }

    if (errors.length > 0) {
      logger.error('Validation errors found in facility data:', errors);
      await session.abortTransaction();
      return [];
    }

    // Check for duplicate facilities
    const facilityNames = facilities.map(f => f.facilityName);
    const existingFacilities = await Facility.find({
      facilityName: { $in: facilityNames }
    }, { facilityName: 1 }).session(session).exec();

    if (existingFacilities.length > 0) {
      logger.error(`Duplicate facilities found: ${existingFacilities.map(f => f.facilityName)}`);
      await session.abortTransaction();
      return [];
    }

    // Insert facilities
    const result = await Facility.insertMany(facilities, { session });
    logger.log(`Successfully migrated ${result.length} facilities to MongoDB`);

    return facilities;
  } catch (error) {
    await session.abortTransaction();
    logger.error('Error in getFacilities, transaction aborted:', error);
    throw error;
  }
}

// export async function getFacilityMap(): Promise<Map<string, Types.ObjectId>> {
//   try {
//     const facilities = await Facility.find({
//       organizationId: new Types.ObjectId(global.config.organizationId)
//     }, { facilityName: 1, _id: 1 }).exec();
//     // return new Map(facilities.map(f => [f.facilityName.toLowerCase(), f._id as Types.ObjectId]));
//     return new Map(facilities.map(f => [f._id.toString(), f._id as Types.ObjectId]));
//   } catch (error) {
//     logger.error('Error fetching facility data:', error);
//     throw error;
//   }
// }

export async function getFacilityIdDocMap(): Promise<Map<string, IFacility>> {
  try {
    const facilities = await Facility.find({
      organizationId: new Types.ObjectId(global.config.organizationId)
    }).exec();
    const map = new Map();
    facilities.forEach(f => {
      map.set(f._id.toString(), f);
      if (f.id) {
        map.set(f.id.toString(), f);
      }
    });
    return map;
  } catch (error) {
    logger.error('Error fetching facility data:', error);
    throw error;
  }
}

// export async function getFacilityIdMap(): Promise<Map<string, string>> {
//   try {
//     const facilities = await Facility.find({}).exec();
//     if (facilities.length === 0) {
//       throw new Error('No facilities found');
//     }
//     const map = new Map();
//     facilities.forEach(f => {
//       map.set(f.id.toString(), f._id.toString());
//       map.set(f._id.toString(), f.facilityName.toString());
//       map.set(f._id.toString(), f._id.toString());
//     });
//     return map
//   } catch (error) {
//     logger.error('Error fetching facility data:', error);
//     throw error;
//   }
// }