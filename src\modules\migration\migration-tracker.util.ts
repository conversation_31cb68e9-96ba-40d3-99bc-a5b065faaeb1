import { MigrationTrackerService } from './migration-tracker.service';
import { MigrationStatus, MigrationType } from './migration-tracker.model';
import logger from '../../common/logger/log.module';

/**
 * Wrapper function to track a migration
 * @param organizationId Organization ID
 * @param migrationType Type of migration
 * @param userId User ID who initiated the migration
 * @param migrationFn Migration function to execute
 * @returns Result of the migration function
 */
export async function trackMigration<T>(
  organizationId: string,
  migrationType: MigrationType,
  userId: string,
  migrationFn: () => Promise<T>
): Promise<T> {
  let tracker;
  try {
    // Start migration tracking
    tracker = await MigrationTrackerService.startMigration(
      organizationId,
      migrationType,
      userId
    );

    // Execute the migration function
    const result = await migrationFn();

    // Complete migration tracking
    await MigrationTrackerService.completeMigration(
      tracker._id.toString(),
      MigrationStatus.COMPLETED
    );

    return result;
  } catch (error) {
    // Log the error
    logger.error(`Error in migration ${migrationType}:`, error);

    // Update migration status to failed
    if (tracker) {
      await MigrationTrackerService.completeMigration(
        tracker._id.toString(),
        MigrationStatus.FAILED
      );
    }

    // Re-throw the error
    throw error;
  }
}

/**
 * Update migration details for a module
 * @param trackerId Migration tracker ID
 * @param module Module name
 * @param recordsProcessed Number of records processed
 * @param recordsSucceeded Number of records succeeded
 * @param recordsFailed Number of records failed
 * @param errors Array of error messages
 */
export async function updateMigrationDetail(
  trackerId: string,
  module: string,
  recordsProcessed: number,
  recordsSucceeded: number,
  recordsFailed: number,
  errors: string[] = []
) {
  try {
    await MigrationTrackerService.updateMigrationDetail(trackerId, {
      module,
      recordsProcessed,
      recordsSucceeded,
      recordsFailed,
      errors
    });
  } catch (error) {
    logger.error(`Error updating migration detail for ${module}:`, error);
  }
}
