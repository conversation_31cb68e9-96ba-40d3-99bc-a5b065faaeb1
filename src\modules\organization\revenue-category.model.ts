
import mongoose, { Schema } from 'mongoose';
import { Document } from 'mongoose';

export interface IRevenueCategory extends Document {
    name: string;
    description?: string;
    isActive: boolean;
}

export const RevenueCategorySchema = new Schema<IRevenueCategory>({
    id: {
        type: String,
        required: false
    },
    name: {
        type: String,
        required: true
    },
    description: {
        type: String,
        required: false
    },
    isActive: {
        type: Boolean,
        required: false,
        default: true
    }
}, { timestamps: true });

export const RevenueCategory = mongoose.model<IRevenueCategory>('RevenueCategory', RevenueCategorySchema);