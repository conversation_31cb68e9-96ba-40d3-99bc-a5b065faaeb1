import { Types } from 'mongoose';
import { Pricing } from '../pricing/pricing.model';
import { InvoiceGenerator, InvoiceGeneratorOptions } from './invoice-generator';
import { ENUM_DISCOUNT_TYPE, ENUM_PRODUCT_ITEM_TYPE } from '../../common/enums/enums';
import { CustomPackage } from '../pricing/custom-package.model';
import Product from '../products/products.model';
import { Inventory } from '../products/inventory.model';


const options: InvoiceGeneratorOptions = {
    userId: '64a4e0666066666666666666',
    organizationId: '64a4e0666066666666666666',
    facilityId: '64a4e0666066666666666666',
    createdBy: '64a4e0666066666666666666',
    invoiceNumber: 1,
    orderId: 1,
    platform: 'web',
    date: new Date(),
    isInclusiveofGst: true,
    billingAddressId: '64a4e0666066666666666666',
    // cartDiscount: 100,
    // cartDiscountType: ENUM_DISCOUNT_TYPE.FLAT,
    clientDetails: {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '1234567890',
    },
    clientBillingDetails: {
        name: 'John Doe',
        addressLine1: '123 Main St',
        addressLine2: '',
        postalCode: '12345',
        cityName: 'New York',
        stateName: 'NY',
        gstNumber: '1234567890',
        utCode: 'UT1',
    },
    billingDetails: {
        name: 'John Doe',
        addressLine1: '123 Main St',
        addressLine2: '',
        postalCode: '12345',
        cityName: 'New York',
        stateName: 'NY',
        gstNumber: '1234567890',
        utCode: 'UT1',
    },
    isForBusiness: false,
};


const generator = new InvoiceGenerator(options);

const pricing = new Pricing({
    id: '1',
    organizationId: new Types.ObjectId(),
    itemType: ENUM_PRODUCT_ITEM_TYPE.SERVICE,
    name: 'Yoga Monthly Pass',
    price: 2999,
    isSellOnline: true,
    tax: 18,
    services: {
        type: 'personalAppointment',
        serviceCategory: new Types.ObjectId(),
        appointmentType: [new Types.ObjectId()],
        relationShip: [],
        sessionType: 'single',
        sessionCount: 1,
        dayPassLimit: 0,
        sessionPerDay: 1,
        introductoryOffer: 'no',
    },
    expiredInDays: 30,
    hsnOrSacCode: '998719',
    durationUnit: 'months',
    membershipId: new Types.ObjectId(),
    promotion: new Types.ObjectId(),
    isActive: true,
    pricingIds: [],
    isBundledPricing: false,
    revenueCategory: new Types.ObjectId(),
    activeTimeFrames: [],
    description: 'Yoga Monthly Pass',
    image: 'https://www.example.com/image.jpg',
    isFeatured: false,
    isTrial: false,
});

const customPackage = new CustomPackage({
    id: '1',
    name: 'Custom Package',
    quantity: 1,
    unitPrice: 1000,
    tax: 18,
    hsnOrSacCode: '998719',
});



generator.addServiceItem(pricing, 1, {
    startDate: new Date(),
    isInclusiveofGst: true,
    discountType: ENUM_DISCOUNT_TYPE.FLAT,
    discountValue: 100,
    discountedBy: '64a4e0666066666666666666',
    promotionLabel: 'Promotion',
    promotionLabelKey: 'promotion',
});

generator.addCustomPackageItem(customPackage);

const product = new Product({
    id: '1',
    name: 'Product',
    hsn: '998719',
    gst: 18,
});

const inventory = new Inventory({
    id: '1',
    product: product._id,
    salePrice: 1000,
    mrp: 1000,
});

generator.addProductItem(product, inventory, 1, undefined, {
    isInclusiveofGst: true,
    discountType: ENUM_DISCOUNT_TYPE.FLAT,
    discountValue: 100,
    discountedBy: '64a4e0666066666666666666',
    promotionLabel: 'Promotion',
    promotionLabelKey: 'promotion',
});

generator.execute();

console.log(generator.invoice);


