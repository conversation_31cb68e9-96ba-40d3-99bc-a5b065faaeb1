import * as path from 'path';
import * as crypto from "crypto";
import { Types } from "mongoose";
import { connectToMongo, closeConnection } from '../../common/database/db.module';
import { parseCSVWithRowNumbers } from '../../common/utils/csv-parser';
import { User, IUser as UserDocument } from './user.model';
import { Role, IRole as RoleDocument } from '../role/role.model';
import { LoggerConfig } from '../../common/logger/log.module';
import { Staff } from './staff.model';
import { Client } from './client.model';
import { ENUM_ROLE_TYPE } from '../role/role.enum';

import uuid4 from "uuid4";
import { IFacility } from '../facility/facility.model';
import { UserMigrationBatchProcessor } from './user.migration.batch';
import { MigrationLogger } from './user.migration.logger';
import { MigrationResultGenerator } from './user.migration.results';
import {
  ICsvUser,
  IBatchConfig,
  DEFAULT_BATCH_CONFIG,
  MigrationPhase
} from './user.migration.types';



const CsvToObjectKeyMapUser: Record<keyof ICsvUser, string> = {
  id: "id",
  firstName: "first name",
  lastName: "last name",
  countryCode: "country code",
  mobile: "mobile",
  email: "email",
  relation: "relation",
  dob: "dob",
  gender: "gender",
  role: "role",
  facilityId: "facility id",
  age: "age",
  isActive: "is active",
  address1: "address 1",
  address2: "address 2",
  city: "city",
  state: "state",
  postalCode: "postal code",
  country: "country",
  photo: "photo",
  emergencyContactPerson: "emergency contact person",
  emergencyContactPhone: "emergency contact phone",
  createdAt: "created at",
}

const logger = LoggerConfig('user.migration');
const duplicateClientsIds = new Set<string>();

/**
 * Migrate user data from CSV to MongoDB using batch processing
 */
export async function migrateUsers(
  _dbName: string = 'hop-migration',
  batchConfig: IBatchConfig = DEFAULT_BATCH_CONFIG
): Promise<void> {
  const migrationLogger = new MigrationLogger();
  let mongoose: any;
  let session: any;

  try {
    // Initialize migration
    migrationLogger.setPhase(MigrationPhase.INITIALIZATION, 'Setting up migration environment');

    // Connect to database
    mongoose = await connectToMongo();
    session = await mongoose.startSession();

    // Load and parse CSV data
    migrationLogger.setPhase(MigrationPhase.DATA_LOADING, 'Loading CSV data into memory');
    const userDataWithRows = await parseCSVWithRowNumbers<ICsvUser>('users.csv', CsvToObjectKeyMapUser);

    if (!userDataWithRows || userDataWithRows.length === 0) {
      logger.info('No user data found in CSV file');
      return;
    }

    // Log migration start with configuration
    migrationLogger.logMigrationStart(userDataWithRows.length, batchConfig.batchSize);

    // Start transaction
    session.startTransaction();

    // Check for duplicate users in database
    migrationLogger.setPhase(MigrationPhase.VALIDATION, 'Validating data and checking for duplicates');
    await checkForDuplicateUsers(userDataWithRows, session);

    // Process users in batches
    migrationLogger.setPhase(MigrationPhase.BATCH_PROCESSING, 'Processing users in batches');
    const batchProcessor = new UserMigrationBatchProcessor(batchConfig);
    const { stats, results } = await batchProcessor.processBatches(userDataWithRows, session);

    // Commit transaction
    await session.commitTransaction();
    logger.info('Transaction committed successfully');

    // Generate result files
    migrationLogger.setPhase(MigrationPhase.RESULT_GENERATION, 'Generating result CSV files');
    const csvFiles = await MigrationResultGenerator.generateResultFiles(results, stats);

    // Generate error report if there are failures
    if (stats.totalFailed > 0) {
      await MigrationResultGenerator.generateErrorReport(results);
    }

    // Log completion
    migrationLogger.setPhase(MigrationPhase.COMPLETED, 'Migration completed successfully');
    migrationLogger.logMigrationComplete(stats, csvFiles);

  } catch (error) {
    logger.error('Error in migration process:', error);

    // Abort transaction if it exists
    if (session) {
      try {
        await session.abortTransaction();
        logger.info('Transaction aborted due to error');
      } catch (abortError) {
        logger.error('Error aborting transaction:', abortError);
      }
    }

    // Log migration failure
    migrationLogger.logMigrationFailure(error instanceof Error ? error : new Error(String(error)));
    throw error;

  } finally {
    // Cleanup
    migrationLogger.setPhase(MigrationPhase.CLEANUP, 'Cleaning up resources');

    if (session) {
      session.endSession();
    }

    await closeConnection();
  }
}

/**
 * Check for duplicate users in the database
 */
async function checkForDuplicateUsers(
  userDataWithRows: Array<{ data: ICsvUser; rowNumber: number }>,
  session: any
): Promise<void> {
  const usernames = userDataWithRows
    .map(({ data }) => data.email || data.mobile)
    .filter(Boolean);

  if (usernames.length === 0) {
    return;
  }

  try {
    const existingUsers = await User.find({
      organizationId: new Types.ObjectId(global.config.organizationId),
      $and: [
        {
          $or: [
            { email: { $in: usernames } },
            { mobile: { $in: usernames } }
          ]
        },
        {
          $or: [
            { parent: null },
            { parent: { $exists: false } }
          ]
        }
      ]
    }, { email: 1, mobile: 1 }).session(session).exec();

    if (existingUsers.length > 0) {
      const duplicateIdentifiers = existingUsers.map((user: any) =>
        `(${user.email || ''} ${user.mobile ? ', ' + user.mobile : ''})`
      );
      const errorMessage = `Duplicate users found in database: ${duplicateIdentifiers.join(', ')}`;
      logger.error(errorMessage);
      throw new Error(errorMessage);
    }
  } catch (error) {
    logger.error('Error checking for duplicate users:', error);
    throw error;
  }
}



export async function getStaff(user: any, csvUser: ICsvUser, organization: string, facilityId: string): Promise<any> {
  const facilityIds = facilityId ? [new Types.ObjectId(facilityId)] : [];

  const address = await getStaffAddress(csvUser.city, csvUser.state, csvUser.address1, csvUser.postalCode, csvUser.country);

  const staff = new Staff({
    id: user.id,
    createdBy: new Types.ObjectId(organization),
    userId: user._id,
    organizationId: new Types.ObjectId(organization),
    facilityId: facilityIds,
    address,
    gender: csvUser.gender?.toLocaleLowerCase(),
    dateOfBirth: csvUser.dob ? new Date(csvUser.dob) : null,
    setUpDate: csvUser.createdAt ? new Date(csvUser.createdAt) : null,
    createdAt: csvUser.createdAt ? new Date(csvUser.createdAt) : new Date(),
  });
  const hash = crypto.createHash('sha256').update(uuid4() + (user.email || user.mobile) + staff._id.toString()).digest('hex');
  const randomId = hash.substring(0, 7);
  staff.staffId = `S-${randomId}`;
  return staff;
}

export async function getClient(user: any, csvUser: ICsvUser, organization: string, facilityId: Types.ObjectId): Promise<any> {
  const address = getClientAddress(csvUser.address1, csvUser.address2, csvUser.city, csvUser.state, csvUser.postalCode, csvUser.country);

  const client = new Client({
    id: user.id,
    createdBy: new Types.ObjectId(organization),
    userId: user._id,
    organizationId: new Types.ObjectId(organization),
    facilityId: new Types.ObjectId(facilityId),
    address,
    dob: csvUser.dob ? new Date(csvUser.dob) : null,
    gender: csvUser?.gender.toLowerCase(),
    relation: csvUser.relation ? csvUser.relation?.toLowerCase() : undefined,
    photo: csvUser.photo,
    emergencyContactPerson: csvUser.emergencyContactPerson,
    emergencyContactPhone: csvUser.emergencyContactPhone,
    createdAt: csvUser.createdAt ? new Date(csvUser.createdAt) : new Date(),
  });

  client.clientId = `C-${generateUniqueClientId(user.email || user.mobile.toString())}`;
  duplicateClientsIds.add(client.clientId);
  return client;
}

function generateUniqueClientId(email) {
  const base62chars = 'OPQRSTUVWX0123456789ABCDEFGHIJKLMNYZabcdefghijklmnopqrstuvwxyz';

  function toBase62(num) {
    let str = '';
    while (num > 0) {
      str = base62chars[num % 62] + str;
      num = Math.floor(num / 62);
    }
    return str.padStart(7, '0').slice(0, 7);
  }

  function simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = (hash * 31 + str.charCodeAt(i)) >>> 0;
    }
    return hash;
  }

  function generateCode(input) {
    const hash = simpleHash(input);
    const base62 = toBase62(hash);
    return `${base62}`;
  }

  let attempts = 0;
  const baseInput = String(email || '');
  let clientId = '';

  if (!baseInput) {
    throw new Error("User must have at least an email or mobile.");
  }

  while (!clientId) {
    const salt = attempts === 0 ? '' : `-${uuid4()}`;
    const input = `${baseInput}${salt}`;
    const code = generateCode(input);

    if (!duplicateClientsIds.has(code)) {
      clientId = code;
      duplicateClientsIds.add(code);
      break;
    }

    attempts++;

    if (attempts > 100) {
      throw new Error("Failed to generate unique client ID after 100 attempts.");
    }
  }

  return clientId;
}

const getStaffAddress = async (city: string, state: string, street: string, postalCode: string, country: string) => {
  // Make sure the maps are initialized
  const stateId = global.stateMap.get(state?.toLowerCase());
  const cityId = global.cityMap.get(city?.toLowerCase());

  const address = {
    stateId,
    cityId,
    street,
    postalCode,
    country,
  };

  if (!address.stateId) {
    logger.warn(`State not found for "${state}". Using default state.`);
    // Instead of throwing an error, we could use a default state ID or null
    address.stateId = null;
  }

  if (!address.cityId) {
    logger.warn(`City not found for "${city}". Using default city.`);
    // Instead of throwing an error, we could use a default city ID or null
    address.cityId = null;
  }

  return address;
}

const getClientAddress = (addressLine1: string, addressLine2: string, city: string, state: string, postalCode: string, country: string) => {

  if (!state) {
    throw new Error(`State is required for client address`);
  }

  const address = {
    addressLine1: addressLine1,
    addressLine2: addressLine2,
    city: city ? global.cityMap.get(city?.toLowerCase()) : null,
    state: state ? global.stateMap.get(state?.toLowerCase()) : null,
    postalCode: postalCode && !isNaN(Number(postalCode)) ? Number(postalCode) : undefined,
    country: country ? country : "",
    isDefault: false
  };

  return address;
}